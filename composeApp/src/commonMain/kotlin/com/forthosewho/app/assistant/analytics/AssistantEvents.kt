package com.forthosewho.app.assistant.analytics

import com.forthosewho.app.analytics.AnalyticsEvent

sealed class AssistantEvents {

    data class AssistantOpened(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Assistant Opened"
    }

    data class VerifyClues(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Verify Clues"
    }

    data class AssistantClosed(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Assistant Closed"
    }

    data class UserReplies(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "User Replies"
    }
}